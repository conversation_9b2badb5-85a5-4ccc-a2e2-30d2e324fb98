'use client';

import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { MOCK_HEADER_MARKDOWN } from '../../constants/mock-header-markdown';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import Radio from '@/shared/components/form/input/Radio';
import Checkbox from '@/shared/components/form/input/Checkbox';

// Form validation schema
const questionnaireSchema = z.object({
  fullName: z.string().optional(),
  tel: z.string().optional(),
  currentAddress: z.string().optional(),
  interviewSite: z.string().optional(),
  sex: z.string().optional(),
  age: z.string().optional(),
  eyeLaserBenefits: z.array(z.string()).optional(),
  otherBenefit: z.string().optional(),
});

type QuestionnaireFormData = z.infer<typeof questionnaireSchema>;

const QuestionnaireSection: React.FC<{ type: string }> = ({ type }) => {
  // Form state management
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<QuestionnaireFormData>({
    resolver: zodResolver(questionnaireSchema),
    defaultValues: {
      fullName: '',
      tel: '',
      currentAddress: '',
      interviewSite: '',
      sex: '',
      age: '',
      eyeLaserBenefits: [],
      otherBenefit: '',
    },
  });

  const onSubmit = (data: QuestionnaireFormData) => {
    console.log('Form data:', data);
  };

  return (
    <>
      {
        type === 'markdown' && (
          <MarkdownRenderer content={MOCK_HEADER_MARKDOWN} />
        )
      }

      {
        type === 'info' && (
          <div>
            {/* header */}
            <div className=" p-1.5 px-4 border-l-4 border-sky-600">
              <h4 className="text-sky-600">Screening Information</h4>
              <div className="">Please provide us your basic information for survey screening process.</div>
            </div>
            {/* content */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4">
              {/* Full name */}
              <div>
                <Label htmlFor="fullName">Full name:</Label>
                <Controller
                  name="fullName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="fullName"
                      type="text"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.fullName}
                      hint={errors.fullName?.message}
                    />
                  )}
                />
              </div>

              {/* Tel */}
              <div>
                <Label htmlFor="tel">Tel:</Label>
                <Controller
                  name="tel"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="tel"
                      type="tel"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.tel}
                      hint={errors.tel?.message}
                    />
                  )}
                />
              </div>

              {/* Current address */}
              <div>
                <Label htmlFor="currentAddress">Current address:</Label>
                <Controller
                  name="currentAddress"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="currentAddress"
                      type="text"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.currentAddress}
                      hint={errors.currentAddress?.message}
                    />
                  )}
                />
              </div>

              {/* Interview/survey site */}
              <div>
                <Label>Interview/survey site:</Label>
                <Controller
                  name="interviewSite"
                  control={control}
                  render={({ field }) => (
                    <div className="flex flex-wrap gap-4 mt-2">
                      {['Hà Nội', 'TP.Vĩnh', 'Đà Nẵng', 'Quy Nhon', 'Cần Thơ', 'TP. Hồ Chí Minh'].map(site => (
                        <Radio
                          key={site}
                          id={`site-${site}`}
                          name="interviewSite"
                          value={site}
                          checked={field.value === site}
                          onChange={field.onChange}
                          label={site}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Sex */}
              <div>
                <Label>Sex:</Label>
                <Controller
                  name="sex"
                  control={control}
                  render={({ field }) => (
                    <div className="flex gap-4 mt-2">
                      {['Male', 'Female'].map(gender => (
                        <Radio
                          key={gender}
                          id={`sex-${gender}`}
                          name="sex"
                          value={gender}
                          checked={field.value === gender}
                          onChange={field.onChange}
                          label={gender}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Age */}
              <div>
                <Label>Age:</Label>
                <Controller
                  name="age"
                  control={control}
                  render={({ field }) => (
                    <div className="flex flex-wrap gap-4 mt-2">
                      {['Under 18', '18 - 24', '25 - 34', '35 - 42', 'Above 42'].map(ageRange => (
                        <Radio
                          key={ageRange}
                          id={`age-${ageRange}`}
                          name="age"
                          value={ageRange}
                          checked={field.value === ageRange}
                          onChange={field.onChange}
                          label={ageRange}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Eye Laser Surgery Benefits */}
              <div>
                <Label>What do you think are the benefits of Eye Laser Surgery? (Select all that apply)</Label>
                <Controller
                  name="eyeLaserBenefits"
                  control={control}
                  render={({ field }) => {
                    const benefits = [
                      'Being able to see clearly without glasses or contact lenses',
                      'Very convenient for daily activities and sports (e.g., driving, watching 3D movies, swimming, playing soccer, etc.)',
                      'Saving money on glasses and lenses',
                      'Permanently cured eye sight conditions',
                      'No more discomfortness when wearing glasses',
                      'Feel more confident about appearance',
                      'Saving time spent on glasses and contact lenses cares',
                      'More confidence and convenience at workplace',
                    ];

                    const handleCheckboxChange = (benefit: string, checked: boolean) => {
                      const currentValues = field.value || [];
                      if (checked) {
                        field.onChange([...currentValues, benefit]);
                      } else {
                        field.onChange(currentValues.filter((item: string) => item !== benefit));
                      }
                    };

                    return (
                      <div className="space-y-3 mt-2">
                        {benefits.map(benefit => (
                          <Checkbox
                            key={benefit}
                            id={`benefit-${benefit}`}
                            label={benefit}
                            checked={(field.value || []).includes(benefit)}
                            onChange={checked => handleCheckboxChange(benefit, checked)}
                          />
                        ))}

                        {/* Other option with text input */}
                        <div className="flex items-start gap-3">
                          <Controller
                            name="eyeLaserBenefits"
                            control={control}
                            render={({ field: benefitsField }) => (
                              <Checkbox
                                id="benefit-other"
                                classNameLabel="shrink-0"
                                label="Other (please specify):"
                                checked={(benefitsField.value || []).includes('Other')}
                                onChange={(checked) => {
                                  const currentValues = benefitsField.value || [];
                                  if (checked) {
                                    benefitsField.onChange([...currentValues, 'Other']);
                                  } else {
                                    benefitsField.onChange(currentValues.filter((item: string) => item !== 'Other'));
                                  }
                                }}
                              />
                            )}
                          />
                          <Controller
                            name="otherBenefit"
                            control={control}
                            render={({ field: otherField }) => (
                              <Input
                                id="otherBenefit"
                                type="text"
                                value={otherField.value}
                                onChange={otherField.onChange}
                                classNameParent="w-full"
                                className="flex-1 p-0 h-[unset] border-0 border-b rounded-b-none w-full focus:border-0 hover:border-0 focus:box-shadow-none"
                                error={!!errors.otherBenefit}
                                hint={errors.otherBenefit?.message}
                              />
                            )}
                          />
                        </div>
                      </div>
                    );
                  }}
                />
              </div>
            </form>
          </div>
        )
      }
    </>
  );
};

export default QuestionnaireSection;
