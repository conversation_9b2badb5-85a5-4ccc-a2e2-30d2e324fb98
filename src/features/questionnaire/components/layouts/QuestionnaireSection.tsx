'use client';

import React from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { MOCK_HEADER_MARKDOWN } from '../../constants/mock-header-markdown';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import Radio from '@/shared/components/form/input/Radio';

// Form validation schema
const questionnaireSchema = z.object({
  fullName: z.string().optional(),
  tel: z.string().optional(),
  currentAddress: z.string().optional(),
  interviewSite: z.string().optional(),
  sex: z.string().optional(),
  age: z.string().optional(),
});

type QuestionnaireFormData = z.infer<typeof questionnaireSchema>;

const QuestionnaireSection: React.FC<{ type: string }> = ({ type }) => {
  // Form state management
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<QuestionnaireFormData>({
    resolver: zodResolver(questionnaireSchema),
    defaultValues: {
      fullName: '',
      tel: '',
      currentAddress: '',
      interviewSite: '',
      sex: '',
      age: '',
    },
  });

  const onSubmit = (data: QuestionnaireFormData) => {
    console.log('Form data:', data);
  };

  return (
    <>
      {
        type === 'markdown' && (
          <MarkdownRenderer content={MOCK_HEADER_MARKDOWN} />
        )
      }

      {
        type === 'info' && (
          <div>
            {/* header */}
            <div className=" p-1.5 px-4 border-l-4 border-sky-600">
              <h4 className="text-sky-600">Screening Information</h4>
              <div className="">Please provide us your basic information for survey screening process.</div>
            </div>
            {/* content */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 p-4">
              {/* Full name */}
              <div>
                <Label htmlFor="fullName">Full name:</Label>
                <Controller
                  name="fullName"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="fullName"
                      type="text"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.fullName}
                      hint={errors.fullName?.message}
                    />
                  )}
                />
              </div>

              {/* Tel */}
              <div>
                <Label htmlFor="tel">Tel:</Label>
                <Controller
                  name="tel"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="tel"
                      type="tel"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.tel}
                      hint={errors.tel?.message}
                    />
                  )}
                />
              </div>

              {/* Current address */}
              <div>
                <Label htmlFor="currentAddress">Current address:</Label>
                <Controller
                  name="currentAddress"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="currentAddress"
                      type="text"
                      placeholder=""
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.currentAddress}
                      hint={errors.currentAddress?.message}
                    />
                  )}
                />
              </div>

              {/* Interview/survey site */}
              <div>
                <Label>Interview/survey site:</Label>
                <Controller
                  name="interviewSite"
                  control={control}
                  render={({ field }) => (
                    <div className="flex flex-wrap gap-4 mt-2">
                      {['Hà Nội', 'TP.Vĩnh', 'Đà Nẵng', 'Quy Nhon', 'Cần Thơ', 'TP. Hồ Chí Minh'].map(site => (
                        <Radio
                          key={site}
                          id={`site-${site}`}
                          name="interviewSite"
                          value={site}
                          checked={field.value === site}
                          onChange={field.onChange}
                          label={site}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Sex */}
              <div>
                <Label>Sex:</Label>
                <Controller
                  name="sex"
                  control={control}
                  render={({ field }) => (
                    <div className="flex gap-4 mt-2">
                      {['Male', 'Female'].map(gender => (
                        <Radio
                          key={gender}
                          id={`sex-${gender}`}
                          name="sex"
                          value={gender}
                          checked={field.value === gender}
                          onChange={field.onChange}
                          label={gender}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>

              {/* Age */}
              <div>
                <Label>Age:</Label>
                <Controller
                  name="age"
                  control={control}
                  render={({ field }) => (
                    <div className="flex flex-wrap gap-4 mt-2">
                      {['Under 18', '18 - 24', '25 - 34', '35 - 42', 'Above 42'].map(ageRange => (
                        <Radio
                          key={ageRange}
                          id={`age-${ageRange}`}
                          name="age"
                          value={ageRange}
                          checked={field.value === ageRange}
                          onChange={field.onChange}
                          label={ageRange}
                        />
                      ))}
                    </div>
                  )}
                />
              </div>
            </form>
          </div>
        )
      }
    </>
  );
};

export default QuestionnaireSection;
