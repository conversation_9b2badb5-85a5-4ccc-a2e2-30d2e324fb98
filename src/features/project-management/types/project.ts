export enum ProjectCampaignEnum {
  CAMPAIGN_1,
  CAMPAIGN_2,
  CAMPAIGN_3,
}

export enum ProjectStatusEnum {
  PLANNED,
  IN_PROGRESS,
  COMPLETED,
  ON_HOLD,
}

export enum ProjectTypeEnum {
  BRANDING,
  GENERAL_CONSULTING,
  DIAGNOSTICS,
}

// Type definitions
type DateRange = {
  from: Date | undefined;
  to: Date | undefined;
};

export type FilterState = {
  status: ProjectStatusEnum | 'All';
  type: ProjectTypeEnum | 'All';
  campaign: ProjectCampaignEnum | 'All';
  startDateRange: DateRange;
  endDateRange: DateRange;
  searchQuery: string;
};

export type SortOption = 'latest' | 'oldest';

export type Project = {
  id: string;
  name: string;
  slugName: string;
  description?: string;
  status: ProjectStatusEnum;
  type: ProjectTypeEnum;
  campaign: ProjectCampaignEnum;
  startDate: string;
  endDate?: string;

  // Client information
  clientName: string;
  address: string;
  taxCode: string;
  contactPerson: string;
  tel: string;
  email: string;
  industry?: string;

  // Team information
  ownedId?: string;
  memberIds: string[];

  // Metadata
  createdAt: string;
  updatedAt: string;
};

export type GetProjectsResponse = {
  items: Project[];
  total: number;
  page: number;
  itemsPerPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
};

export type questionResponseCrew = {
  question: string;
  answer: string;
  id: string;
};

export type documentUrlCrew = {
  id: string;
  url: string;
  originalname: string;
  filename: string;
  key: string;
};

export type fileUploadResponse = {
  infos: stepInfoFile[];
};

export type stepInfoFile = {
  file: string;
  name: string;
  type: string;
  id: string;
};
