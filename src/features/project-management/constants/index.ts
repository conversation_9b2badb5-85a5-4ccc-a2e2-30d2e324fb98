import { ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '../types/project';

export const PROJECT_TYPE_LABEL: { [key: string]: string } = {
  [ProjectTypeEnum.BRANDING]: 'project_type_options.branding', // ProjectTypeEnum.BRANDING
  [ProjectTypeEnum.GENERAL_CONSULTING]: 'project_type_options.general_consulting', // ProjectTypeEnum.GENERAL_CONSULTING
  [ProjectTypeEnum.DIAGNOSTICS]: 'project_type_options.diagnostics', // ProjectTypeEnum.DIAGNOSTICS
};

export const PROJECT_STATUS_LABEL: { [key: string]: string } = {
  [ProjectStatusEnum.PLANNED]: 'status_options.planned', // ProjectStatusEnum.PLANNED
  [ProjectStatusEnum.IN_PROGRESS]: 'status_options.in_progress', // ProjectStatusEnum.IN_PROGRESS
  [ProjectStatusEnum.COMPLETED]: 'status_options.completed', // ProjectStatusEnum.COMPLETED
  [ProjectStatusEnum.ON_HOLD]: 'status_options.on_hold', // ProjectStatusEnum.ON_HOLD
};

export const PROJECT_CAMPAIGN_LABEL: { [key: string]: string } = {
  [ProjectCampaignEnum.CAMPAIGN_1]: 'campaign_options.campaign_1', // ProjectCampaignEnum.CAMPAIGN_1
  [ProjectCampaignEnum.CAMPAIGN_2]: 'campaign_options.campaign_2', // ProjectCampaignEnum.CAMPAIGN_2
  [ProjectCampaignEnum.CAMPAIGN_3]: 'campaign_options.campaign_3', // ProjectCampaignEnum.CAMPAIGN_3
};

export const DEFAULT_PAGE_SIZE = 10;
