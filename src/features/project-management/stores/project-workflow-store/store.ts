import { createStore, useStore as useZustandStore } from 'zustand';
import type { ProjectWorkflowStoreSelector, ProjectWorkflowStoreShape } from './types';
import { createEvaluationSlice } from './slices/createEvaluationSlice';
import { createProjectDetailSlice } from './slices/createProjectDetailSlice';
import { createTaskWorkflowSlice } from './slices/createTaskWorkflowSlice';
import { createWorkflowSlice } from './slices/createWorkflowSlice';
import { use } from 'react';
import { ProjectStoreContext } from './context';

/**
 * Creates a Project Store with all slices combined
 *
 * This function creates a Zustand store that combines all project management slices:
 * - WorkflowSlice: Handles workflow management and navigation
 * - TaskWorkflowSlice: Handles simplified task workflow
 * - EvaluationSlice: Handles evaluation forms and scoring
 *
 * The store follows the slice pattern from the Dify workflow store implementation,
 * allowing for modular, composable state management with proper TypeScript support.
 *
 * @returns A Zustand store instance with all project management functionality
 *
 * @example
 * ```typescript
 * // Basic usage
 * const store = createProjectWorkflowStore();
 *
 * // With project ID
 * const store = createProjectWorkflowStore({ projectId: 'project-123' });
 *
 * // With custom slice injection
 * const store = createProjectWorkflowStore({
 *   injectSliceFn: (set, get) => ({
 *     customAction: () => console.log('Custom action'),
 *     customState: 'initial value'
 *   })
 * });
 * ```
 */
export const createProjectWorkflowStore = () => {
  return createStore<ProjectWorkflowStoreShape>((...args) => {
    const projectDetailSlice = createProjectDetailSlice(...args);
    const workflowSlice = createWorkflowSlice(...args);
    const taskWorkflowSlice = createTaskWorkflowSlice(...args);
    const evaluationSlice = createEvaluationSlice(...args);

    return {
      ...projectDetailSlice,
      ...workflowSlice,
      ...taskWorkflowSlice,
      ...evaluationSlice,
      actions: {
        ...projectDetailSlice.actions,
        ...workflowSlice.actions,
        ...taskWorkflowSlice.actions,
        ...evaluationSlice.actions,
      },
    };
  });
};

export function useWorkflowStoreSelector<T>(selector: ProjectWorkflowStoreSelector<T>): T {
  // Get the store instance from context
  const storeContext = use(ProjectStoreContext);

  if (!storeContext) {
    throw new Error(
      'useProjectStoreContext must be used within a ProjectWorkflowSstoretoreProvider. '
      + 'Make sure to wrap your component tree with <ProjectWorkflowStoreProvider>.',
    );
  }

  // Use Zustand's useStore hook with the selector
  return useZustandStore(storeContext, selector);
}

export const useWorkflowStore = () => {
  return use(ProjectStoreContext);
};
