'use client';

import Label from '@/shared/components/form/Label';
import FileUpload from '../initial-screening-form/FileUpload';
import React, { useEffect, useState } from 'react';
import type { IFileResponse } from '@/shared/types/global';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useCurrentStep, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useCoAgent, useCopilotChat } from '@copilotkit/react-core';
import { useGetInfoDetail, useProjectDetail } from '@/features/project-management/hooks';
import { useParams } from 'next/navigation';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { Role, TextMessage } from 'node_modules/@copilotkit/runtime-client-gql/dist/client/types';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import type { fileUploadResponse, stepInfoFile } from '@/features/project-management/types/project';
import { Env } from '@/core/config/Env';

const ClientUploadForm: React.FC = () => {
  const [_files, setFiles] = useState<IFileResponse[]>([]);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  // Custom Hook

  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { data: fileUpload } = useGetInfoDetail<fileUploadResponse>(currentStep?.id ?? '');

  //  Set Agent

  const { setState: setCoAgentsState } = useCoAgent<BriefAnalysisFlow>({
    name: 'brief_analysis_flow',
    initialState: {},
  });

  const { appendMessage } = useCopilotChat();

  // project detail

  const params = useParams<{ id: string }>();

  const { data: project } = useProjectDetail(params.id);

  const updateInitialFile = () => {
    const setFile = () => {
      setInitialFile((fileUpload?.stepInfo[0]?.infos ?? []).map(
        (info: stepInfoFile) => ({
          mimeType: info.type,
          originalname: info.name,
          key: info.file,
          filename: info.name,
          url: `${Env.NEXT_PUBLIC_API_SERVER}/public/${info.file}`,
          _id: info.id,
        }),
      ));
    };

    setFile();
  };

  useEffect(() => {
    if (fileUpload && fileUpload.stepInfo.length) {
      updateInitialFile();
    }
  }, [fileUpload]);

  const setProjectIdInAgent = React.useCallback(() => {
    if (project) {
      setCoAgentsState(coAgentState => ({
        ...coAgentState,
        project_id: project?.id,
      }));
    }
  }, [project]);

  useEffect(() => {
    setProjectIdInAgent();
  }, [setProjectIdInAgent]);

  const {
    completeStep,
  } = useWorkflowActions();

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const handleSendMessage = () => {
    setCoAgentsState(prevState => ({
      ...prevState,
      template_brief_url: getFile(_files),
    }));

    appendMessage(
      new TextMessage({
        content: 'Trigger Brief Analysis',
        role: Role.Developer,
      }),
    );
  };

  const onSubmit = async () => {
    if (!currentStepId) {
      return;
    }
    const payload = {
      stepInfos: [
        {
          order: 0,
          infos: _files.map(file => ({
            file: file.key,
            name: file.originalname,
            type: file.mimeType,
            id: file._id,
          })),
        },
      ],
    };

    await updateQuestionAnswer(
      payload,
      currentStepId,
    );

    handleSendMessage();

    completeStep(currentStepId);
  };

  return (
    <div className="relative">
      <div className="space-y-6 p-4 md:p-6">
        <Label htmlFor="files" className="mb-1.5 block text-primary">
          Attached Files
        </Label>
        <FileUpload initialFile={initialFile} onFilesChange={handleFilesChange} />
      </div>

      <WorkflowNavigation
        onComplete={onSubmit}
        nextButtonText="Generate"
        showPrevious={false}
      />
    </div>
  );
};

export default ClientUploadForm;
