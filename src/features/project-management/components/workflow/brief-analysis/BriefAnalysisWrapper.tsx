'use client';

import { But<PERSON> } from '@/shared/components/ui/button';
import BriefAnalysisView from './BriefAnalysisView';
import { MOCK_MARKDOWN_DATA } from '@/features/project-management/constants/mock-markdown';
import { ArrowDownTrayIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import { useEffect, useRef, useState } from 'react';
import BriefAnalysisEditor from './BriefAnalysisEditor';
import { useCoAgent } from '@copilotkit/react-core';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';

const BriefAnalysisWrapper: React.FC = () => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const editorRef = useRef<any>(null);

  const { state } = useCoAgent<BriefAnalysisFlow>({
    name: 'brief_analysis_flow',
    initialState: {},
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setIsLoading(false);
    };
    updateState();
  };

  useEffect(() => {
    console.log('brief_analysis_flow', state);
    if (state.brief_analysis_output && state.brief_analysis_process && state.brief_analysis_process === 'done') {
      updateMarkdownToState(state.brief_analysis_output);
    } else if (state.brief_analysis_process && state.brief_analysis_process === 'done') {
      updateMarkdownToState(MOCK_MARKDOWN_DATA);
    }
  }, [state]);

  const toggleEditMode = () => {
    setIsEditMode(prev => !prev);
  };

  const handleSubmit = () => {
    setIsEditMode(false);
    if (editorRef.current) {
      editorRef.current.handleSubmitForm();
    }
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6">
            <Button
              type="button"
              variant="outline"
              onClick={toggleEditMode}
            >
              <FileEditIcon className="h-5 w-5 " />
              Edit
            </Button>

            <Button
              type="button"
              variant="outline"
            >
              <ArrowDownTrayIcon className="h-5 w-5 " />
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
            >
              <CheckBadgeIcon className="h-5 w-5 " />
              Approve
            </Button>
          </div>

          <div className="">
            {
              isEditMode
                ? <BriefAnalysisEditor ref={editorRef} content={markdown} />

                : <BriefAnalysisView content={markdown} />
            }
          </div>
        </div>
      );
};

export default BriefAnalysisWrapper;
