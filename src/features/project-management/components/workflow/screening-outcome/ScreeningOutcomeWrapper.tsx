import { useCoAgent } from '@copilotkit/react-core';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import ScreeningOutcomeMetrics from './ScreeningOutcomeMetrics';
import { useEffect, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useCurrentStep, useCurrentTask, useEvaluationActions, useOverallScore, useScoreDetail, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import type { ScoreDetail } from '@/features/project-management/types/evaluation';
import ScreenOutcomeCharts from './ScreenOutcomeCharts';

export default function ScreeningOutcomeWrapper() {
  const [_isLoadingData, setIsLoadingData] = useState<boolean>(true);

  const { mutateAsync } = useUpdateStatusStep();

  const { state: stateAssessmentState } = useCoAgent<any>({
    name: 'client_assessment_flow',
    initialState: {},
  });

  const {
    updateInitialEvaluationData,
    updateScoreDetail,
  } = useEvaluationActions();

  const scoreDetailStore = useScoreDetail();

  const {
    completeStep,
  } = useWorkflowActions();

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const { data: outComeData } = useGetInfoDetail<any>(currentStep?.id ?? '');

  const overallScore = useOverallScore();

  const previousId = currentTask?.steps[1]?.id;

  const { data: scoreDetail } = useGetInfoDetail<ScoreDetail>(previousId ?? '');

  // TODO: enhance it later
  useEffect(() => {
    if (!overallScore.score) {
      updateScoreDetail(scoreDetail as any);
    } else {
      setIsLoadingData(false);
    }
  }, [scoreDetail, overallScore]);

  useEffect(() => {
    if (scoreDetailStore) {
      updateInitialEvaluationData(scoreDetailStore as any);
    }
  }, [scoreDetailStore, updateInitialEvaluationData]);

  useEffect(() => {
    if ((stateAssessmentState.final_process && stateAssessmentState.final_process === 'done')
      || (outComeData && outComeData.stepInfo.length)
    ) {
      setIsLoadingData(false);
    }
  }, [stateAssessmentState, outComeData]);

  const handleSubmit = () => {
    mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });

    completeStep(currentStep?.id ?? '');
  };

  return (
    _isLoadingData
      ? (
          <div className=" h-full p-4 md:p-6 ">
            <ProjectCardSkeleton />
          </div>
        )
      : (
          <div className="relative">
            <div className="space-y-7 p-4 md:p-6">
              <ScreeningOutcomeMetrics />
              <ScreenOutcomeCharts />
            </div>

            <WorkflowNavigation
              onComplete={handleSubmit}
              nextButtonText="Complete & Move to Next Task"
              showPrevious={false}
            />
          </div>
        )
  );
}
